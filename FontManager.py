import os
import sys
import platform
import json
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import subprocess
from fpdf import FPDF


class FontManager:
    """
    Comprehensive font management system for CV generation.
    Handles system font detection, custom font loading, validation, and preview.
    """
    
    def __init__(self, config_file: str = "font_config.json"):
        self.config_file = config_file
        self.system_fonts = {}
        self.custom_fonts = {}
        self.supported_formats = ['.ttf', '.otf']
        self.fallback_fonts = ['arial', 'helvetica', 'times', 'courier']
        self.config = self._load_config()
        
    def _load_config(self) -> Dict:
        """Load font configuration from file."""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"Warning: Could not load config file: {e}")
        
        # Default configuration
        return {
            "selected_font": None,
            "custom_font_paths": [],
            "font_preferences": {},
            "last_used_fonts": []
        }
    
    def _save_config(self):
        """Save current configuration to file."""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"Warning: Could not save config file: {e}")
    
    def detect_system_fonts(self) -> Dict[str, str]:
        """
        Detect available system fonts across different operating systems.
        Returns a dictionary mapping font names to their file paths.
        """
        system = platform.system().lower()
        font_paths = []
        
        if system == "windows":
            font_paths = [
                os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'Fonts'),
                os.path.join(os.environ.get('LOCALAPPDATA', ''), 'Microsoft', 'Windows', 'Fonts')
            ]
        elif system == "darwin":  # macOS
            font_paths = [
                "/System/Library/Fonts",
                "/Library/Fonts",
                os.path.expanduser("~/Library/Fonts")
            ]
        else:  # Linux and other Unix-like systems
            font_paths = [
                "/usr/share/fonts",
                "/usr/local/share/fonts",
                os.path.expanduser("~/.fonts"),
                os.path.expanduser("~/.local/share/fonts")
            ]
        
        fonts = {}
        for font_dir in font_paths:
            if os.path.exists(font_dir):
                fonts.update(self._scan_font_directory(font_dir))
        
        self.system_fonts = fonts
        return fonts
    
    def _scan_font_directory(self, directory: str) -> Dict[str, str]:
        """Scan a directory for font files."""
        fonts = {}
        try:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in self.supported_formats):
                        font_path = os.path.join(root, file)
                        font_name = os.path.splitext(file)[0].lower()
                        fonts[font_name] = font_path
        except PermissionError:
            pass  # Skip directories we can't access
        except Exception as e:
            print(f"Warning: Error scanning {directory}: {e}")
        
        return fonts
    
    def add_custom_font(self, font_path: str, font_name: Optional[str] = None) -> bool:
        """
        Add a custom font from a file path.
        Returns True if successful, False otherwise.
        """
        if not os.path.exists(font_path):
            print(f"Error: Font file not found: {font_path}")
            return False
        
        if not any(font_path.lower().endswith(ext) for ext in self.supported_formats):
            print(f"Error: Unsupported font format. Supported formats: {', '.join(self.supported_formats)}")
            return False
        
        if not self._validate_font_file(font_path):
            print(f"Error: Invalid or corrupted font file: {font_path}")
            return False
        
        if font_name is None:
            font_name = os.path.splitext(os.path.basename(font_path))[0].lower()
        
        self.custom_fonts[font_name] = font_path
        
        # Update config
        if font_path not in self.config["custom_font_paths"]:
            self.config["custom_font_paths"].append(font_path)
            self._save_config()
        
        print(f"Successfully added custom font: {font_name}")
        return True
    
    def _validate_font_file(self, font_path: str) -> bool:
        """
        Validate that a font file is not corrupted and can be loaded.
        """
        try:
            # Try to create a temporary PDF to test font loading
            temp_pdf = FPDF()
            temp_pdf.add_page()
            
            # Extract font name for testing
            font_name = os.path.splitext(os.path.basename(font_path))[0]
            
            # Try to add the font
            temp_pdf.add_font(font_name, '', font_path, uni=True)
            temp_pdf.set_font(font_name, size=12)
            temp_pdf.cell(0, 10, "Test", ln=True)
            
            return True
        except Exception:
            return False
    
    def get_all_fonts(self) -> Dict[str, Dict[str, str]]:
        """
        Get all available fonts (system and custom).
        Returns a dictionary with 'system' and 'custom' keys.
        """
        if not self.system_fonts:
            self.detect_system_fonts()
        
        return {
            'system': self.system_fonts,
            'custom': self.custom_fonts
        }
    
    def search_fonts(self, query: str) -> Dict[str, str]:
        """Search for fonts by name (case-insensitive)."""
        all_fonts = {**self.system_fonts, **self.custom_fonts}
        query_lower = query.lower()
        
        return {
            name: path for name, path in all_fonts.items()
            if query_lower in name.lower()
        }
    
    def get_font_info(self, font_name: str) -> Optional[Dict]:
        """Get detailed information about a specific font."""
        all_fonts = {**self.system_fonts, **self.custom_fonts}
        
        if font_name.lower() not in all_fonts:
            return None
        
        font_path = all_fonts[font_name.lower()]
        font_type = "custom" if font_name.lower() in self.custom_fonts else "system"
        
        return {
            "name": font_name,
            "path": font_path,
            "type": font_type,
            "size": os.path.getsize(font_path) if os.path.exists(font_path) else 0,
            "format": os.path.splitext(font_path)[1].lower()
        }
    
    def preview_font(self, font_name: str, sample_text: str = "Sample CV Text - Przykładowy tekst CV") -> bool:
        """
        Generate a preview of how the font will look in a CV context.
        Creates a temporary PDF file for preview.
        """
        all_fonts = {**self.system_fonts, **self.custom_fonts}

        if font_name.lower() not in all_fonts:
            print(f"Error: Font '{font_name}' not found")
            return False

        font_path = all_fonts[font_name.lower()]

        try:
            # Create preview PDF
            preview_pdf = FPDF()
            preview_pdf.add_page()

            # Add the font (use a clean font name for PDF)
            clean_font_name = font_name.lower().replace('-', '_')
            preview_pdf.add_font(clean_font_name, '', font_path, uni=True)

            # Create preview content with title using regular style
            preview_pdf.set_font(clean_font_name, size=16)
            preview_pdf.cell(0, 10, f"Font Preview: {font_name}", ln=True, align='C')
            preview_pdf.ln(5)

            # Different sizes
            sizes = [14, 12, 10, 8]
            for size in sizes:
                preview_pdf.set_font(clean_font_name, size=size)
                preview_pdf.cell(0, 8, f"Size {size}: {sample_text}", ln=True)
                preview_pdf.ln(2)

            # Save preview
            preview_path = f"font_preview_{font_name.replace('/', '_')}.pdf"
            preview_pdf.output(preview_path)

            print(f"Font preview saved as: {preview_path}")
            return True

        except Exception as e:
            print(f"Error creating font preview: {e}")
            return False
    
    def set_selected_font(self, font_name: str) -> bool:
        """Set the selected font for CV generation."""
        all_fonts = {**self.system_fonts, **self.custom_fonts}
        
        if font_name.lower() not in all_fonts:
            print(f"Error: Font '{font_name}' not found")
            return False
        
        self.config["selected_font"] = font_name.lower()
        
        # Update last used fonts
        if font_name.lower() in self.config["last_used_fonts"]:
            self.config["last_used_fonts"].remove(font_name.lower())
        self.config["last_used_fonts"].insert(0, font_name.lower())
        
        # Keep only last 5 used fonts
        self.config["last_used_fonts"] = self.config["last_used_fonts"][:5]
        
        self._save_config()
        print(f"Selected font: {font_name}")
        return True
    
    def get_selected_font(self) -> Optional[Tuple[str, str]]:
        """
        Get the currently selected font.
        Returns tuple of (font_name, font_path) or None if no font selected.
        """
        selected = self.config.get("selected_font")
        if not selected:
            return None
        
        all_fonts = {**self.system_fonts, **self.custom_fonts}
        if selected in all_fonts:
            return selected, all_fonts[selected]
        
        return None
    
    def get_fallback_font(self) -> Tuple[str, Optional[str]]:
        """
        Get a fallback font if the selected font is not available.
        Returns tuple of (font_name, font_path or None for built-in fonts).
        """
        all_fonts = {**self.system_fonts, **self.custom_fonts}
        
        # Try fallback fonts in order
        for fallback in self.fallback_fonts:
            if fallback in all_fonts:
                return fallback, all_fonts[fallback]
        
        # Return first available system font
        if self.system_fonts:
            first_font = next(iter(self.system_fonts.items()))
            return first_font[0], first_font[1]
        
        # Last resort - use built-in FPDF fonts
        return 'arial', None
