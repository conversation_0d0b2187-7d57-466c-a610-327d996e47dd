# CV Generator with Advanced Font Selection System

A comprehensive Python-based CV generator with an advanced font selection system that supports both system fonts and custom fonts. Create professional CVs with your preferred typography.

## 🌟 Features

- **Advanced Font Selection**: Choose from system fonts or add custom fonts (TTF, OTF)
- **Interactive Font Browser**: Browse, search, and preview fonts before selection
- **Font Preview System**: Generate PDF previews to see how fonts look in your CV
- **Cross-Platform Support**: Works on Windows, macOS, and Linux
- **Automatic Font Detection**: Automatically detects available system fonts
- **Font Validation**: Validates font files to ensure compatibility
- **Fallback System**: Robust fallback mechanism for missing fonts
- **Configuration Management**: Saves font preferences and recently used fonts
- **Multi-language Support**: Supports Unicode characters for international text

## 📋 Requirements

- Python 3.7 or higher
- Required Python packages (install via pip):
  ```bash
  pip install fpdf2 Pillow
  ```

## 🚀 Quick Start

1. **Clone or download** this repository
2. **Install dependencies**:
   ```bash
   pip install fpdf2 Pillow
   ```
3. **Run the CV generator**:
   ```bash
   python CV.py
   ```
4. **Follow the interactive prompts** to select fonts and enter your CV data

## 🎨 Font Selection Options

### Option 1: Interactive Font Selection Interface
- Browse all available fonts with pagination
- Search fonts by name
- Preview fonts before selection
- Add custom fonts
- View recently used fonts

### Option 2: Quick Font Selection
- Enter a font name directly
- System automatically searches for the font
- Falls back to default if not found

### Option 3: Default Font
- Uses the system default font (Arial)

## 📁 Supported Font Formats

- **TTF** (TrueType Font)
- **OTF** (OpenType Font)

## 🔧 Font Management

### Adding Custom Fonts

1. **Through Interactive Interface**:
   - Run `python CV.py`
   - Choose option 1 (font selection interface)
   - Select "Add custom font" from the menu
   - Enter the path to your font file

2. **Direct Font Selector**:
   ```bash
   python FontSelector.py
   ```

### Font Storage Locations

The system automatically detects fonts from these locations:

**Windows:**
- `C:\Windows\Fonts`
- `%LOCALAPPDATA%\Microsoft\Windows\Fonts`

**macOS:**
- `/System/Library/Fonts`
- `/Library/Fonts`
- `~/Library/Fonts`

**Linux:**
- `/usr/share/fonts`
- `/usr/local/share/fonts`
- `~/.fonts`
- `~/.local/share/fonts`

## 📖 Usage Examples

### Basic Usage
```bash
python CV.py
```

### Font Selection Only
```bash
python FontSelector.py
```

### Using Custom Fonts
1. Download a font file (e.g., from [Google Fonts](https://fonts.google.com/))
2. Save it to your computer
3. Run the CV generator
4. Choose "Add custom font" and provide the path
5. Preview and select the font

## 🛠️ Configuration

The system creates a `font_config.json` file to store:
- Selected font preferences
- Custom font paths
- Recently used fonts
- Font-specific settings

Example configuration:
```json
{
    "selected_font": "roboto",
    "custom_font_paths": [
        "/path/to/custom/font.ttf"
    ],
    "font_preferences": {},
    "last_used_fonts": [
        "roboto",
        "arial",
        "helvetica"
    ]
}
```

## 🔍 Font Preview System

The font preview system generates sample PDFs showing:
- Font name and type
- Multiple font sizes (14, 12, 10, 8)
- Sample text in your language
- How the font appears in CV context

Preview files are saved as `font_preview_[fontname].pdf`

## 🚨 Troubleshooting

### Common Issues and Solutions

#### Font Not Found
**Problem**: "Font 'fontname' not found"
**Solutions**:
- Check font name spelling
- Ensure font is installed on your system
- Try adding the font as a custom font
- Use the font browser to see available fonts

#### Font Loading Error
**Problem**: "Error loading font" or corrupted font file
**Solutions**:
- Verify the font file is not corrupted
- Check file permissions
- Ensure the font format is supported (TTF/OTF)
- Try downloading the font again

#### Permission Errors
**Problem**: Cannot access font directories
**Solutions**:
- Run with appropriate permissions
- Copy fonts to user-accessible directories
- Use custom font loading instead of system fonts

#### PDF Generation Fails
**Problem**: CV generation fails with font errors
**Solutions**:
- The system will automatically fall back to Arial
- Check the console for specific error messages
- Try selecting a different font
- Ensure all required data is provided

### Font Compatibility

**Supported Features**:
- Unicode characters (international text)
- Bold and regular weights
- Various font sizes
- Cross-platform compatibility

**Limitations**:
- Italic fonts require separate font files
- Some decorative fonts may not render properly in PDFs
- Very large font files may slow down processing

## 📝 File Structure

```
CV Generator/
├── CV.py                 # Main CV generation script
├── FontManager.py        # Font management system
├── FontSelector.py       # Interactive font selection interface
├── Languages.py          # Language support
├── UserTemplates.py      # User data templates
├── font_config.json      # Font configuration (auto-generated)
├── README.md            # This documentation
└── photo.jpg            # Your photo (add your own)
```

## 🎯 Tips for Best Results

1. **Font Selection**:
   - Choose readable fonts for professional CVs
   - Avoid overly decorative fonts
   - Test fonts with preview before final selection

2. **Custom Fonts**:
   - Download fonts from reputable sources
   - Keep font files organized in a dedicated folder
   - Use descriptive names when adding custom fonts

3. **Performance**:
   - System font detection may take a moment on first run
   - Large font files may slow down preview generation
   - Recently used fonts load faster

4. **Compatibility**:
   - TTF fonts generally have better compatibility
   - Test your CV on different devices/systems
   - Keep a backup with a standard font

## 🤝 Contributing

Feel free to contribute to this project by:
- Reporting bugs
- Suggesting new features
- Improving documentation
- Adding font support for additional formats

## 📄 License

This project is open source. Feel free to use and modify according to your needs.

## 🆘 Support

If you encounter issues:
1. Check this README for solutions
2. Verify your Python and package versions
3. Test with default fonts first
4. Check file permissions and paths

---

**Happy CV Creating! 🎉**
