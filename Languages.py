import json

class Languages:
    def __init__(self, lang='en'):
        self.lang = lang
        self.translation = {}
    def get_translations(self):
        try:
            with open(f'{self.lang}.json', 'r', encoding='utf-8') as f:
                self.translation = json.load(f)
        except FileNotFoundError:
            print(f"Could not find '{self.lang}.json' language file")
        except Exception as e:
            print(f"Unknown error has appeared {e}")
    def set_own_translation(self, trans_name):
        try:
            with open(f'{trans_name}.json', 'w', encoding='utf-8') as f:
                education = input(f"'Education' in '{self.lang}' language")
                tel = input(f"'Telephon Number' in '{self.lang}' language")
                mail = 'E-mail'
                address = input(f"'Address' in '{self.lang}' language")
                job_exp = input(f"'Job experience' in '{self.lang}' language")
                abilities = input(f"'Abilities' in '{self.lang}' language")
                for_langs = input(f"'Foreign Languages' in '{self.lang}' language")
                hobbies = input(f"'Hobbies' in '{self.lang}' language")
                self.translation = {f"{'education': {education}}, {'tel': {tel}}, {'E-mail': {mail}}, {'address': {address}}, {'Job Experience': {job_exp}}, {'Abilities': {abilities}}, {'Foreign Languages': {for_langs}}, {'Hobbies': {hobbies}},"}
                json.dump(self.translation, f, ensure_ascii=False, indent=4)
        except FileExistsError:
            print(f"The translation '{trans_name}' is now exist. Please try different name or choose another translation")
        except Exception as e:
            print(f"Unknown error has appeared {e}")
