import json

class UserTemplates:
    def __init__(self, full_name):
        self.full_name = full_name
    def set_profile(self):
        try:
            with open(f'{self.full_name}.json', 'w', encoding='utf-8') as f:
                photo_name = input('Provide name of your photo in /images/')
                tel = input(f'Provide your phone number')
                education = input('Provide your last degree of education')
                job_exp = input('Provide your previous workplaces. Use dots to divide them')
                abilities = input('Provide your abilities. Use dots to divide them')
                for_langs = input('Provide languages, you know. Use dots to divide them')
                hobbies = input('Provide your hobbies. Use dots to divide them')
                profile = {f"{'Full Name': {self.full_name}}, {'Photo Name': {photo_name}}, {'Telephone Number': {tel}}, {'Education': {education}} , {'Job Experience ': {job_exp.split('.')}}, {'Abilities': {abilities.split('.')}}, {'Foreign Languages': {for_langs.split('.')}}, {'Hobbies': {hobbies.split('.')}}"}
                json.dump(profile, f, ensure_ascii=False, indent=4)
        except FileExistsError:
            print(f"The '{self.full_name}.json' file is now exist. Please try different name")
        except Exception as e:
            print(f"Unknown error has appeared {e}")
    def get_profile(self):
        try:
            with open(f'{self.full_name}.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Could not find '{self.full_name}.json' profile file")
        except Exception as e:
            print(f"Unknown error has appeared {e}")