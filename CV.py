from fpdf import FPDF
from PIL import Image
from Languages import Languages
from UserTemplates import UserTemplates
from FontManager import <PERSON>ontManager
from FontSelector import FontSelector
import os
import sys

def get_font_selection():
    """Get font selection from user with new font system."""
    print("\n🎨 FONT SELECTION")
    print("-" * 40)
    print("1. Use font selection interface")
    print("2. Quick select (enter font name)")
    print("3. Use default font (arial)")

    choice = input("Choose option (1-3): ").strip()

    font_manager = FontManager()

    if choice == '1':
        # Launch interactive font selector
        selector = FontSelector()
        selector.run()

        # Get the selected font
        selected = font_manager.get_selected_font()
        if selected:
            return selected[0], selected[1]
        else:
            print("No font selected. Using default.")
            return font_manager.get_fallback_font()

    elif choice == '2':
        # Quick font name input
        font_name = input('Enter font name (or press Enter for arial): ').strip()
        if not font_name:
            font_name = 'arial'

        # Detect system fonts if not done
        if not font_manager.system_fonts:
            print("Detecting fonts... Please wait.")
            font_manager.detect_system_fonts()

        # Try to find the font
        all_fonts = {**font_manager.system_fonts, **font_manager.custom_fonts}
        if font_name.lower() in all_fonts:
            font_manager.set_selected_font(font_name)
            return font_name.lower(), all_fonts[font_name.lower()]
        else:
            print(f"Font '{font_name}' not found. Using fallback.")
            return font_manager.get_fallback_font()

    else:
        # Use default
        return font_manager.get_fallback_font()

def get_user_data():
    """Collect user data for CV generation."""
    print("\n📝 CV DATA COLLECTION")
    print("-" * 40)

    full_name = input('Provide your name: ')
    education = input('Provide your education/degree: ')
    tel = input('Provide your phone number: ')
    mail = input('Provide your email: ')
    address = input('Provide your address: ')

    # Work experience
    print("\nWork Experience (enter each job on a new line, press Enter twice when done):")
    work_exp = []
    while True:
        job = input("Job: ").strip()
        if not job:
            break
        work_exp.append(job)

    if not work_exp:
        work_exp = ["No work experience listed"]

    # Education details
    school_desc = input('Describe your education in detail: ')

    # Abilities
    print("\nAbilities/Skills (enter each skill on a new line, press Enter twice when done):")
    abilities = []
    while True:
        ability = input("Skill: ").strip()
        if not ability:
            break
        abilities.append(ability)

    if not abilities:
        abilities = ["No abilities listed"]

    # Foreign languages
    print("\nForeign Languages (enter each language on a new line, press Enter twice when done):")
    for_langs = []
    while True:
        lang = input("Language: ").strip()
        if not lang:
            break
        for_langs.append(lang)

    if not for_langs:
        for_langs = ["No foreign languages listed"]

    # Hobbies
    print("\nHobbies (enter each hobby on a new line, press Enter twice when done):")
    hobbies = []
    while True:
        hobby = input("Hobby: ").strip()
        if not hobby:
            break
        hobbies.append(hobby)

    if not hobbies:
        hobbies = ["No hobbies listed"]

    return {
        'full_name': full_name,
        'education': education,
        'tel': tel,
        'mail': mail,
        'address': address,
        'work_exp': work_exp,
        'school_desc': school_desc,
        'abilities': abilities,
        'for_langs': for_langs,
        'hobbies': hobbies,
        'work_exp_t': 'Work Experience',
        'school_t': 'Education',
        'abilities_t': 'Skills & Abilities',
        'for_langs_t': 'Foreign Languages',
        'hobbies_t': 'Hobbies & Interests'
    }

# Get font selection
font_name, font_path = get_font_selection()

# Get user data
user_data = get_user_data()
full_name = user_data['full_name']
user = UserTemplates(full_name)

class PDF(FPDF):
    def __init__(self, font_name, user_data):
        super().__init__()
        self.font_name = font_name
        self.user_data = user_data

    def header(self):
        # Użycie czcionki, która obsługuje polskie znaki
        self.set_font(self.font_name, 'B', 18)
        self.cell(0, 10, self.user_data['full_name'], ln=True, align='C') # Centruj nagłówek
        self.set_font(self.font_name, size=12)
        self.cell(0, 10, self.user_data['education'], ln=True, align='C') # Centruj podtytuł
        self.ln(5)

# Ścieżki
photo_path = "photo.jpg" # Upewnij się, że to zdjęcie istnieje
pdf_path = f"CV - {full_name}.pdf"

# Inicjalizacja PDF
pdf = PDF(font_name, user_data)

# Dodanie niestandardowej czcionki obsługującej polskie znaki
def setup_font(pdf, font_name, font_path):
    """Setup font for PDF with proper error handling."""
    try:
        if font_path and os.path.exists(font_path):
            # Custom font file
            pdf.add_font(font_name, '', font_path, uni=True)
            print(f"✅ Successfully loaded custom font: {font_name}")
            return font_name
        else:
            # Try to use built-in font or fallback
            try:
                pdf.set_font(font_name, size=12)
                print(f"✅ Using built-in font: {font_name}")
                return font_name
            except:
                # Use fallback font
                fallback_font = 'arial'
                pdf.set_font(fallback_font, size=12)
                print(f"⚠️  Using fallback font: {fallback_font}")
                return fallback_font
    except Exception as e:
        print(f"❌ Error loading font '{font_name}': {e}")
        print("Using fallback font: arial")
        fallback_font = 'arial'
        pdf.set_font(fallback_font, size=12)
        return fallback_font

# Setup the font
active_font = setup_font(pdf, font_name, font_path)


pdf.add_page()
pdf.set_auto_page_break(auto=True, margin=15)

# Dodanie zdjęcia - upewnij się, że obraz jest kwadratowy i ma odpowiednie proporcje
try:
    with Image.open(photo_path) as img:
        img_width, img_height = img.size
        # Zmiana rozmiaru zdjęcia na kwadrat, jeśli nie jest
        if img_width != img_height:
            size = min(img_width, img_height)
            img = img.crop(((img_width - size) // 2, (img_height - size) // 2,
                            (img_width + size) // 2, (img_height + size) // 2))
            img.save("temp_square_photo.jpg") # Zapisz tymczasowy kwadratowy plik
            photo_path_used = "temp_square_photo.jpg"
        else:
            photo_path_used = photo_path
    pdf.image(photo_path_used, x=160, y=10, w=40)
except FileNotFoundError:
    print(f"Błąd: Nie znaleziono pliku zdjęcia: {photo_path}")
except Exception as e:
    print(f"Wystąpił błąd podczas przetwarzania zdjęcia: {e}")

# Treść podstawowa
pdf.set_font(active_font, size=11)
pdf.set_text_color(40, 40, 40)

# Usprawnione formatowanie danych kontaktowych
pdf.set_xy(15, 45) # Ustawienie pozycji, aby nie kolidowało z nagłówkiem i zdjęciem
pdf.multi_cell(0, 7, f"Telefon: {user_data['tel']}\nE-mail: {user_data['mail']}\nAdres: {user_data['address']}")

# Doświadczenie
pdf.ln(5)
pdf.set_font(active_font, 'B', 14)
pdf.cell(0, 10, user_data['work_exp_t'], ln=True)
pdf.set_font(active_font, size=11)

# Format work experience properly
work_exp_text = ""
for i, job in enumerate(user_data['work_exp']):
    work_exp_text += f"- {job}\n"

pdf.multi_cell(0, 8, work_exp_text.strip())

# Wykształcenie
pdf.ln(5)
pdf.set_font(active_font, 'B', 14)
pdf.cell(0, 10, user_data['school_t'], ln=True)
pdf.set_font(active_font, size=11)
pdf.multi_cell(0, 8, user_data['school_desc'])

# Umiejętności
pdf.ln(5)
pdf.set_font(active_font, 'B', 14)
pdf.cell(0, 10, user_data['abilities_t'], ln=True)
pdf.set_font(active_font, size=11)
for ability in user_data['abilities']:
    pdf.cell(0, 8, f"• {ability}", ln=True)

# Języki
pdf.ln(5)
pdf.set_font(active_font, 'B', 14)
pdf.cell(0, 10, user_data['for_langs_t'], ln=True)
pdf.set_font(active_font, size=11)
for for_lang in user_data['for_langs']:
    pdf.cell(0, 8, f"• {for_lang}", ln=True)

# Zainteresowania
pdf.ln(5)
pdf.set_font(active_font, 'B', 14)
pdf.cell(0, 10, user_data['hobbies_t'], ln=True)
pdf.set_font(active_font, size=11)
for hobby in user_data['hobbies']:
    pdf.cell(0, 8, f"• {hobby}", ln=True)

# Klauzula RODO
pdf.ln(5)
pdf.set_font(active_font, size=8) # Mniejsza czcionka dla RODO
pdf.multi_cell(0, 4,
"Wyrażam zgodę na przetwarzanie moich danych osobowych dla potrzeb niezbędnych do realizacji procesu rekrutacji zgodnie z Rozporządzeniem Parlamentu Europejskiego i Rady (UE) 2016/679 z dnia 27 kwietnia 2016 r. w sprawie ochrony osób fizycznych w związku z przetwarzaniem danych osobowych i w sprawie swobodnego przepływu takich danych oraz uchylenia dyrektywy 95/46/WE (RODO).")


# Zapisz PDF
pdf.output(pdf_path)

print(f"CV zostało wygenerowane pomyślnie: {pdf_path}")