import os
import sys
from typing import Optional, List, Dict
from FontManager import FontManager


class FontSelector:
    """
    Interactive command-line interface for font selection.
    Provides browsing, searching, preview, and selection capabilities.
    """
    
    def __init__(self):
        self.font_manager = FontManager()
        self.current_page = 0
        self.items_per_page = 10
        
    def display_banner(self):
        """Display the application banner."""
        print("\n" + "="*60)
        print("           CV FONT SELECTION SYSTEM")
        print("="*60)
        print("Choose from system fonts or add custom fonts")
        print("Supports TTF and OTF formats")
        print("="*60 + "\n")
    
    def display_menu(self):
        """Display the main menu options."""
        print("\n📋 MAIN MENU:")
        print("1. Browse available fonts")
        print("2. Search fonts")
        print("3. Add custom font")
        print("4. Preview font")
        print("5. Select font for CV")
        print("6. View current selection")
        print("7. View recently used fonts")
        print("8. Remove custom font")
        print("9. Help")
        print("0. Exit")
        print("-" * 40)
    
    def browse_fonts(self):
        """Browse all available fonts with pagination."""
        print("\n🔍 BROWSING FONTS")
        print("-" * 40)
        
        # Detect fonts if not already done
        if not self.font_manager.system_fonts:
            print("Detecting system fonts... Please wait.")
            self.font_manager.detect_system_fonts()
        
        all_fonts = self.font_manager.get_all_fonts()
        system_fonts = list(all_fonts['system'].keys())
        custom_fonts = list(all_fonts['custom'].keys())
        
        print(f"Found {len(system_fonts)} system fonts and {len(custom_fonts)} custom fonts")
        
        if not system_fonts and not custom_fonts:
            print("No fonts found. Please add custom fonts or check your system.")
            return
        
        # Display fonts with pagination
        all_font_names = system_fonts + custom_fonts
        total_pages = (len(all_font_names) - 1) // self.items_per_page + 1
        
        while True:
            start_idx = self.current_page * self.items_per_page
            end_idx = min(start_idx + self.items_per_page, len(all_font_names))
            
            print(f"\nPage {self.current_page + 1} of {total_pages}")
            print("-" * 40)
            
            for i, font_name in enumerate(all_font_names[start_idx:end_idx], 1):
                font_type = "📁 System" if font_name in system_fonts else "📎 Custom"
                print(f"{i:2d}. {font_name:<30} [{font_type}]")
            
            print("-" * 40)
            print("Commands: [n]ext, [p]revious, [s]elect number, [b]ack to menu")
            
            choice = input("Enter command: ").strip().lower()
            
            if choice == 'n' and self.current_page < total_pages - 1:
                self.current_page += 1
            elif choice == 'p' and self.current_page > 0:
                self.current_page -= 1
            elif choice == 'b':
                self.current_page = 0
                break
            elif choice.isdigit():
                idx = int(choice) - 1
                if 0 <= idx < len(all_font_names[start_idx:end_idx]):
                    selected_font = all_font_names[start_idx + idx]
                    self.show_font_details(selected_font)
            else:
                print("Invalid command. Try again.")
    
    def search_fonts(self):
        """Search for fonts by name."""
        print("\n🔍 SEARCH FONTS")
        print("-" * 40)
        
        query = input("Enter search term: ").strip()
        if not query:
            print("Search cancelled.")
            return
        
        # Ensure fonts are detected
        if not self.font_manager.system_fonts:
            print("Detecting system fonts... Please wait.")
            self.font_manager.detect_system_fonts()
        
        results = self.font_manager.search_fonts(query)
        
        if not results:
            print(f"No fonts found matching '{query}'")
            return
        
        print(f"\nFound {len(results)} fonts matching '{query}':")
        print("-" * 40)
        
        for i, (font_name, font_path) in enumerate(results.items(), 1):
            font_type = "📁 System" if font_name in self.font_manager.system_fonts else "📎 Custom"
            print(f"{i:2d}. {font_name:<30} [{font_type}]")
        
        print("-" * 40)
        choice = input("Enter number to view details (or press Enter to continue): ").strip()
        
        if choice.isdigit():
            idx = int(choice) - 1
            font_names = list(results.keys())
            if 0 <= idx < len(font_names):
                self.show_font_details(font_names[idx])
    
    def add_custom_font(self):
        """Add a custom font file."""
        print("\n📎 ADD CUSTOM FONT")
        print("-" * 40)
        
        font_path = input("Enter path to font file (TTF/OTF): ").strip()
        if not font_path:
            print("Operation cancelled.")
            return
        
        # Expand user path
        font_path = os.path.expanduser(font_path)
        
        custom_name = input("Enter custom name (or press Enter for auto-name): ").strip()
        custom_name = custom_name if custom_name else None
        
        print("Adding font... Please wait.")
        success = self.font_manager.add_custom_font(font_path, custom_name)
        
        if success:
            print("✅ Font added successfully!")
            preview_choice = input("Would you like to preview this font? (y/n): ").strip().lower()
            if preview_choice == 'y':
                font_name = custom_name or os.path.splitext(os.path.basename(font_path))[0].lower()
                self.font_manager.preview_font(font_name)
        else:
            print("❌ Failed to add font. Please check the file path and format.")
    
    def preview_font(self):
        """Preview a selected font."""
        print("\n👁️  FONT PREVIEW")
        print("-" * 40)
        
        font_name = input("Enter font name to preview: ").strip()
        if not font_name:
            print("Preview cancelled.")
            return
        
        custom_text = input("Enter custom preview text (or press Enter for default): ").strip()
        
        print("Generating preview... Please wait.")
        success = self.font_manager.preview_font(
            font_name, 
            custom_text if custom_text else None
        )
        
        if success:
            print("✅ Preview generated successfully!")
            print("Check the generated PDF file for font preview.")
        else:
            print("❌ Failed to generate preview. Font may not be available.")
    
    def select_font(self):
        """Select a font for CV generation."""
        print("\n✅ SELECT FONT FOR CV")
        print("-" * 40)
        
        font_name = input("Enter font name to select: ").strip()
        if not font_name:
            print("Selection cancelled.")
            return
        
        success = self.font_manager.set_selected_font(font_name)
        
        if success:
            print("✅ Font selected successfully!")
            print(f"Current selection: {font_name}")
        else:
            print("❌ Failed to select font. Please check the font name.")
    
    def view_current_selection(self):
        """View the currently selected font."""
        print("\n📋 CURRENT FONT SELECTION")
        print("-" * 40)
        
        selected = self.font_manager.get_selected_font()
        
        if selected:
            font_name, font_path = selected
            font_info = self.font_manager.get_font_info(font_name)
            
            print(f"Selected Font: {font_name}")
            print(f"Type: {font_info['type'].title()}")
            print(f"Path: {font_path}")
            print(f"Format: {font_info['format'].upper()}")
            print(f"Size: {font_info['size'] / 1024:.1f} KB")
        else:
            print("No font currently selected.")
            print("The system will use the default fallback font.")
    
    def view_recent_fonts(self):
        """View recently used fonts."""
        print("\n🕒 RECENTLY USED FONTS")
        print("-" * 40)
        
        recent_fonts = self.font_manager.config.get("last_used_fonts", [])
        
        if not recent_fonts:
            print("No recently used fonts.")
            return
        
        for i, font_name in enumerate(recent_fonts, 1):
            font_info = self.font_manager.get_font_info(font_name)
            if font_info:
                print(f"{i}. {font_name} [{font_info['type'].title()}]")
        
        print("-" * 40)
        choice = input("Enter number to select font (or press Enter to continue): ").strip()
        
        if choice.isdigit():
            idx = int(choice) - 1
            if 0 <= idx < len(recent_fonts):
                self.font_manager.set_selected_font(recent_fonts[idx])
                print(f"✅ Selected: {recent_fonts[idx]}")
    
    def remove_custom_font(self):
        """Remove a custom font."""
        print("\n🗑️  REMOVE CUSTOM FONT")
        print("-" * 40)
        
        custom_fonts = self.font_manager.custom_fonts
        
        if not custom_fonts:
            print("No custom fonts to remove.")
            return
        
        print("Custom fonts:")
        for i, font_name in enumerate(custom_fonts.keys(), 1):
            print(f"{i}. {font_name}")
        
        choice = input("Enter number to remove (or press Enter to cancel): ").strip()
        
        if choice.isdigit():
            idx = int(choice) - 1
            font_names = list(custom_fonts.keys())
            if 0 <= idx < len(font_names):
                font_to_remove = font_names[idx]
                del self.font_manager.custom_fonts[font_to_remove]
                
                # Update config
                font_path = custom_fonts[font_to_remove]
                if font_path in self.font_manager.config["custom_font_paths"]:
                    self.font_manager.config["custom_font_paths"].remove(font_path)
                    self.font_manager._save_config()
                
                print(f"✅ Removed: {font_to_remove}")
    
    def show_font_details(self, font_name: str):
        """Show detailed information about a font."""
        print(f"\n📄 FONT DETAILS: {font_name}")
        print("-" * 40)
        
        font_info = self.font_manager.get_font_info(font_name)
        
        if not font_info:
            print("Font information not available.")
            return
        
        print(f"Name: {font_info['name']}")
        print(f"Type: {font_info['type'].title()}")
        print(f"Path: {font_info['path']}")
        print(f"Format: {font_info['format'].upper()}")
        print(f"Size: {font_info['size'] / 1024:.1f} KB")
        
        print("\nActions:")
        print("1. Preview this font")
        print("2. Select this font")
        print("3. Back to list")
        
        choice = input("Choose action (1-3): ").strip()
        
        if choice == '1':
            self.font_manager.preview_font(font_name)
        elif choice == '2':
            self.font_manager.set_selected_font(font_name)
            print(f"✅ Selected: {font_name}")
        # Choice 3 or any other input returns to previous menu
    
    def show_help(self):
        """Display help information."""
        print("\n❓ HELP")
        print("-" * 40)
        print("Font Selection System Help:")
        print()
        print("• Browse Fonts: View all available system and custom fonts")
        print("• Search: Find fonts by name (partial matches supported)")
        print("• Add Custom Font: Load TTF/OTF files from your computer")
        print("• Preview: Generate a PDF preview of how the font looks")
        print("• Select Font: Choose a font for CV generation")
        print("• Current Selection: View your currently selected font")
        print("• Recent Fonts: Quick access to recently used fonts")
        print()
        print("Supported Formats: TTF, OTF")
        print("Font files are validated before adding to ensure compatibility.")
        print()
        print("Tips:")
        print("• Use descriptive names when adding custom fonts")
        print("• Preview fonts before selecting to ensure readability")
        print("• The system automatically falls back to safe fonts if needed")
        print("-" * 40)
    
    def run(self):
        """Run the interactive font selection interface."""
        self.display_banner()
        
        while True:
            self.display_menu()
            choice = input("Enter your choice (0-9): ").strip()
            
            if choice == '0':
                print("\nGoodbye! 👋")
                break
            elif choice == '1':
                self.browse_fonts()
            elif choice == '2':
                self.search_fonts()
            elif choice == '3':
                self.add_custom_font()
            elif choice == '4':
                self.preview_font()
            elif choice == '5':
                self.select_font()
            elif choice == '6':
                self.view_current_selection()
            elif choice == '7':
                self.view_recent_fonts()
            elif choice == '8':
                self.remove_custom_font()
            elif choice == '9':
                self.show_help()
            else:
                print("❌ Invalid choice. Please try again.")
            
            input("\nPress Enter to continue...")


if __name__ == "__main__":
    selector = FontSelector()
    selector.run()
